Always
---
description: 
globs:
alwaysApply: true
---
# 分布式微服务项目开发规范

## 项目架构概述

### 系统架构
- 基于Spring Cloud的分布式微服务架构
- 服务发现和配置管理：Nacos
- 分布式任务调度：XXL-Job
- 消息队列：RocketMQ
- 数据存储：MongoDB、MySQL
- 缓存：Redis
- 搜索引擎：Elasticsearch（Es）
- 使用gitlab做版本控制，使用ci/cd做持续集成
- 所有微服务模块发布到k8s上运行，由Rancher管理

### 模块组织规范
典型的微服务项目应包含以下模块，每个模块是一个maven工程项目：
- `{project}-admin-service`（管理后台RPC服务）
- `{project}-front-service`（前端RPC服务）
- `{project}-common`（RPC服务、consumer、task的共用模块）
- `{project}-task`（后台定时任务模块）
- `{project}-consumer`（消息处理模块）
- `{project}-front-api`（前端API接口模块）
- `{project}-admin-api`（管理后台API接口模块）

注意：{project}-admin-service、{project}-front-service、{project}-common里又由2个maven模块组成分别是contract和provider
```
project总目录
├── {project}-common/      # 公共maven模块
│   ├── contract/          # 契约层maven模块
│   │   ├── constant/      # 常量定义
│   │   └── enums/         # 枚举定义
│   └── provider/          # 服务层maven模块
│       ├── biz/           # 业务逻辑
│       ├── cache/         # 缓存层
│       ├── dao/           # 数据访问层
│       └── po/            # 持久化对象
├── {project}-consumer/    # 消息消费者maven模块
│   ├── consumers/         # 具体消费者类实现
│   ├── facade/            # 外观层
│   ├── mapper/            # 对象映射
│   └── msg/               # 消息对象
├── {project}-task/        # 定时任务maven模块
│   ├── task/              # 具体任务类实现
│   ├── facade/            # 外观层
│   └── param/             # task参数对象
├── {project}-admin-api/   # 管理后台API maven模块
│   ├── controller/        # 具体业务接口类实现
│   ├── facade/            # 外观层
│   └── param/             # 接口参数对象
├── {project}-admin-service/   # 管理后台服务maven模块
│   ├── contract/          # 契约层maven模块
│   │   ├── servcie/       # 业务接口类
│   │   └── dto/           # 接口参数与返回值对象
│   └── provider/          # 服务层maven模块
│       ├── controller/    # 业务接口实现类
│       ├── biz/           # 业务逻辑
│       └── mapper/        # 对象映射
├── {project}-front-api/   # 前端API maven模块
│   ├── controller/        # 具体业务接口类实现
│   ├── facade/            # 外观层
│   ├── param/             # 接口参数对象
└── {project}-front-service/# 前端服务
    ├── contract/          # 契约层maven模块
    │   ├── servcie/       # 业务接口类
    │   └── dto/           # 接口参数与返回值对象
    └── provider/          # 服务层maven模块
        ├── controller/    # 业务接口实现类
        ├── biz/           # 业务逻辑
        └── mapper/        # 对象映射

```

#### 模块之间依赖关系
- {project}-front-api 依赖 {project}-front-service里的contract模块
- {project}-admin-api 依赖 {project}-admin-service里的contract模块
- {project}-common里的provider模块依赖同级的contract
- {project}-admin-servcie、{project}-front-service、{project}-consumer、{project}-task 依赖 {project}-common里的provider模块

#### 各模块及包结构规范
每个模块下包结构采用maven标准结构
即：
src/main/java/wanda.{project}.{模块名}.{包名}
src/test/java/wanda.{project}.{模块名}.{包名}

##### 其中{project}-front-api、{project}-admin-api、{project}-consumer、 {project}-task、以及{project}-admin-service、{project}-front-service、{project}-common里的contract模块
还包含src/main/java/resources

## 代码风格规范

### 类命名规范
- 实体类：以`Po`结尾（如：`MissionPo`、`RecordPo`）
- DTO类：以`Dto`结尾（如：`MemberDto`）
- VO类：以`Vo`结尾（如：`MissionVo`）
- 枚举类：以`Enum`结尾（如：`MissionTypeEnum`、`ErrorCodeEnum`）
- 消费者类：以`Consumer`结尾（如：`PersonalConsumer`）
- 业务逻辑类：以`Biz`结尾（如：`PersonalMissionExecuteBiz`）
- 检查器类：以`Checker`结尾（如：`BrownPageChecker`）
- 缓存类：以`Cache`结尾（如：`MissionCache`）
- 映射器类：以`Mapper`结尾（如：`PersonalMapper`）
- 外观类：以`Facade`结尾（如：`MemberFacade`）

### 方法命名规范
- 查询方法：以`get`、`find`、`list`开头
- 业务处理方法：以`process`、`execute`、`handle`开头
- 缓存操作方法：以`set`、`delete`、`remove`开头
- 保存方法：使用`save`（新增或更新）
- 分页查询：使用`pageList`

### 注解使用规范

#### 类级别注解
- Controller类：`@Slf4j` + `@RestController` + `@AllArgsConstructor`
- Service/Biz类：`@Slf4j` + `@Component` + `@AllArgsConstructor`（或使用@Autowired）
- Dao类：`@Repository`
- 消费者类：`@Slf4j` + `@MessageConsumer(topic = Topic.XXX)`
- 实体类：`@Data` + `@Entity`（MongoDB）或`@Table`（MySQL）
- 缓存类：`@Component` + `@AllArgsConstructor`

#### @author注解格式
```java
/**
 * 类描述
 *
 * <AUTHOR>
 **/
```

### http接口注释规范
- {project}-admin-api和{project}-front-api模块里接口注释规范要符合idea插件apiDoc规范，用于自动生成文档

## 开发最佳实践

### 日志记录规范
- 所有Controller方法必须记录入参和返回值
- 关键业务操作必须记录日志
- 使用`JsonUtils.encode()`序列化复杂对象
- 日志格式：`log.info("操作描述入参:{}", param)`、`log.info("操作描述返回:{}", result)`

### 异常处理规范
- 使用统一的错误码枚举`ErrorCodeEnum`
- 使用`R`类型作为统一响应格式
- 使用`RMapper.toR(ErrorCodeEnum.SUCCESS)`构建成功响应

### 消息处理模式
消费者处理消息的标准流程：
1. 记录接收到的消息日志
2. 获取会员信息（如需要）
3. 构建业务参数对象
4. 调用业务逻辑处理
5. 记录处理结果日志

### 数据访问规范

#### MongoDB使用规范
```java
@Entity(value = "collection_name", noClassnameStored = true)
@AutoIncrementId  // 自增ID
public class EntityPo implements DbEntity<Long> {
    @Id
    private Long id;
    // 其他字段...
}
```

#### MySQL分片规范
```java
@Table(name = "table_name")
@Sharding(dbKeys = "member_no")  // 分片键
public class EntityPo implements DbEntity<Long> {
    @Id
    private Long id;
    // 其他字段...
}
```

#### Dao层实现规范
- MongoDB：继承`MgoDao<EntityPo, Long>`
- MySQL单库：继承`JsdReadWriteDao<EntityPo, Long>`
```java
@Slf4j
@Repository
public class FlowInstanceDao extends JsdReadWriteDao<FlowInstance, Long> {

    private static final String APPROVAL_OBJECT_CODE = "approval_object_code";
    private static final String DATA_ID = "data_id";
    private static final String INSTANCE_STATUS = "instance_status";
    private static final String CREATE_TIME = "create_time";

    public FlowInstanceDao() {
        super(DB_NAME);
    }

    public FlowInstance queryWriteDB(long instanceId) {
        return openWrite().select(FlowInstance.class).where(f("id", EQ, instanceId)).result().one(FlowInstance.class);
    }

    public List<String> isNeedApproval(String approvalObjectCode, List<String> dataIds, String userId) {
        String sql = "SELECT" +
                " instance.data_id, node.notify_user_ids, node.notify_user_name" +
                " FROM" +
                " flow_instance instance," +
                " flow_node node" +
                " WHERE" +
                " instance.id = node.instance_id" +
                " AND instance.instance_status IN (3, 4)" +
                " AND instance.approval_object_code = '" + approvalObjectCode + "'" +
                " AND instance.data_id IN (" + joinDBStr(dataIds) + ")" +
                " AND node.node_status = 2" +
                " AND find_in_set(" + userId + ", node.notify_user_ids) >= 1";
        log.info(">>> isNeedApproval sql:{}", sql);
        try (ExecuteResult result = openRead().execute(sql).result()) {
            List<String> approveIds = Lists.newArrayList();
            result.each(item -> approveIds.add(item.getString(1)));
            return approveIds;
        }
    }

    public List<Long> isNeedApproval(List<Long> instanceIds, String userId) {
        String sql = "SELECT" +
                " instance.id, node.notify_user_ids, node.notify_user_name" +
                " FROM" +
                " flow_instance instance," +
                " flow_node node" +
                " WHERE" +
                " instance.id = node.instance_id" +
                " AND instance.instance_status IN (3, 4)" +
                " AND instance.id IN (" + instanceIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")" +
                " AND node.node_status = 2" +
                " AND find_in_set(" + userId + ", node.notify_user_ids) >= 1";
        log.info(">>> isNeedApproval sql:{}", sql);
        try (ExecuteResult result = openRead().execute(sql).result()) {
            List<Long> approveInstanceIds = Lists.newArrayList();
            result.each(item -> approveInstanceIds.add(item.getLong(1)));
            return approveInstanceIds;
        }
    }

    public FlowInstance queryRecentlyOne(String approvalObjectCode, String dataId) {
        log.info("查询最新一条审批流, dataId:{}, approvalObjectCode:{}", dataId, approvalObjectCode);
        return openWrite().select(FlowInstance.class)
                .where(
                        f(APPROVAL_OBJECT_CODE, approvalObjectCode)
                                .add(DATA_ID, dataId)
                )
                .orderBy(new Sorters(SortType.DESC, "update_time"))
                .limit(0, 1)
                .result()
                .one(FlowInstance.class);
    }

    public List<FlowInstance> query(String approvalObjectCode, String dataId, List<ApprovalStatus> statusList) {
        return openWrite()
                .select(FlowInstance.class)
                .where(
                        f(APPROVAL_OBJECT_CODE, approvalObjectCode)
                                .add(DATA_ID, dataId)
                                .add(INSTANCE_STATUS, IN, statusList.stream().map(ApprovalStatus::value).toArray())
                )
                .result().all(FlowInstance.class);
    }

    public List<FlowInstance> query(String dataId, String createDateStr, String host, String path) {
        BasicFilter f = f(DATA_ID, dataId).add("approval_url like '%" + host + "/" + path + "%'");
        if (Strings.isNotEmpty(createDateStr)) {
            LocalDate createDate = LocalDate.parse(createDateStr);
            f.add("create_time", GTE, createDate.atTime(0, 0, 0))
                    .add("create_time", LTE, createDate.atTime(23, 59, 59));
        }
        return openRead()
                .select(FlowInstance.class)
                .where(f)
                .result().all(FlowInstance.class);
    }

    public boolean isExist(String approvalObjectCode, String dataId, List<ApprovalStatus> statusList) {
        return openWrite()
                .select(FlowInstance.class)
                .where(
                        f(APPROVAL_OBJECT_CODE, approvalObjectCode)
                                .add(DATA_ID, dataId)
                                .add(INSTANCE_STATUS, IN, statusList.stream().map(ApprovalStatus::value).toArray())
                ).limit(0, 1)
                .result().one(FlowInstance.class) != null;
    }

    public void update(long instanceId, ApprovalStatus instanceStatus) {
        openWrite()
                .update("flow_instance")
                .set(uv(INSTANCE_STATUS, instanceStatus).add("update_time", LocalDateTime.now()))
                .where(f("id", instanceId))
                .result();
    }

    public FlowInstance save(FlowInstance instance) {
        LocalDateTime now = LocalDateTime.now();
        instance.setCreateTime(now);
        instance.setUpdateTime(now);
        openWrite().begin(tx -> {
            long id = tx.insert(instance).result(true).firstKey(Long.class);
            insertFlowNode(tx, id, instance.getFlowNodeList());
            instance.setId(id);
        });
        return instance;
    }

    private void insertFlowNode(Transaction tx, long instanceId, List<FlowNode> nodes) {
        if (nodes == null) return;
        LocalDateTime now = LocalDateTime.now();
        nodes.forEach(node -> {
            node.setInstanceId(instanceId);
            node.setCreateTime(now);
            node.setNodeStatus(NodeStatus.CREATED);
        });
        tx.insert(nodes).result(false);
    }

    public PageResult<FlowInstance> selectPage(FlowInstanceManageQueryParam param) {
        JsdQuery jsdQuery = JsdQuery.create();
        BasicFilter filter = f();
        if (Strings.isNotEmpty(param.getApprovalObjectCode()))
            filter.add(APPROVAL_OBJECT_CODE, param.getApprovalObjectCode());
        if (Strings.isNotEmpty(param.getDataId()))
            filter.add(DATA_ID, param.getDataId());
        if (param.getCreateStartTime() != null)
            filter.add(CREATE_TIME, GTE, param.getCreateStartTime().atTime(0, 0, 0));
        if (param.getCreateEndTime() != null)
            filter.add(CREATE_TIME, LTE, param.getCreateEndTime().atTime(23, 59, 59));
        if (param.getStatus() != 0)
            filter.add(INSTANCE_STATUS, param.getStatus());
        if (param.getInstanceId() != 0)
            filter.add("id", param.getInstanceId());
        if (Strings.isNotEmpty(param.getCreateUserName()))
            filter.add("create_user_name", LK, "%" + param.getCreateUserName() + "%");
        jsdQuery.and(filter);
        return super.selectPage(jsdQuery, new PageInfo(param.getPageIndex(), param.getPageSize()));
    }

    public PageResult<FlowInstance> selectPage(MySubmitApprovalQueryParam param) {
        JsdQuery jsdQuery = JsdQuery.create();
        BasicFilter filter = f("create_user_id", param.getCreateUserId());
        if (Strings.isNotEmpty(param.getApprovalObjectCode()))
            filter.add(APPROVAL_OBJECT_CODE, param.getApprovalObjectCode());
        if (param.getStatus() != null)
            filter.add(INSTANCE_STATUS, param.getStatus());
        if (param.getCreateStartTime() != null)
            filter.add(CREATE_TIME, FilterType.GTE, param.getCreateStartTime());
        if (param.getCreateEndTime() != null)
            filter.add(CREATE_TIME, LTE, param.getCreateEndTime());
        jsdQuery.and(filter).sort(SortType.DESC, CREATE_TIME);
        return super.selectPage(jsdQuery, param.getPageInfo());
    }

    public List<FlowInstanceCreator> queryCreator(String createUserName) {
        String sql = " SELECT " +
                " distinct flow_instance.create_user_id," +
                " flow_instance.create_user_name " +
                " FROM " +
                " flow_instance " +
                " WHERE " +
                " flow_instance.create_user_name " +
                " LIKE " + "'%" + createUserName + "%'";
        log.info(">>> Instance sql:{}" + sql);
        try (ExecuteResult result = openRead().execute(sql).result()) {
            return result.all(FlowInstanceCreator.class);
        }
    }

    public FlowInstance getById(long instanceId) {
        return openWrite().select(FlowInstance.class).where(f("id", instanceId)).result().one(FlowInstance.class);
    }

    public List<FlowInstance> list(List<Long> instanceIds) {
        return selectList(f("id", IN, instanceIds));
    }

    public List<FlowInstance> getFlowInstanceList(List<Long> instanceIds, int status, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        log.info(">>>查询审批实例，instanceIds:{} status:{} startDateTime:{}, endDateTime:{}", instanceIds, status, startDateTime, endDateTime);
        BasicFilter f = f();
        if (!CollectionUtils.isEmpty((instanceIds))) {
            f.add("id", IN, instanceIds);
        }
        if (status != 0) {
            f.add("instance_status", status);
        }
        if (startDateTime != null) {
            f.add("update_time", GTE, startDateTime);
        }
        if (endDateTime != null) {
            f.add("update_time", LT, endDateTime);
        }
        List<FlowInstance> flowInstanceList =
                openRead()
                .select(FlowInstance.class)
                .where(f)
                .result().all(FlowInstance.class);
        log.info(">>>查询审批实例，instanceIds:{} status:{} startDateTime:{}, endDateTime:{}, results:{}", instanceIds, status, startDateTime, endDateTime, JSON.toJSONString(flowInstanceList));
        return flowInstanceList;
    }
}
```
- MySQL分片：继承`JsdShardDao<EntityPo, Long>`
- 使用MySQL分片的话统一使用 指定分片号 作为分片键

### 缓存使用规范
- 缓存类使用`@Component`注解
- 提供`delete()`方法清除缓存
- 数据变更后及时清除相关缓存
- 使用配置文件管理缓存TTL

### 测试规范
- 测试类使用org.junit.jupiter.api包下的`@Test`注解标记方法
- 测试方法命名：`test{功能}`
- 为核心Biz类也提供全面场景单元测试
- 提供完整的HTTP请求测试示例

#### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class MemberServiceTest {
    
    @Mock
    private MemberDao memberDao;
    
    @InjectMocks
    private MemberService memberService;
    
    @Test
    void testFindByNo() {
        // Given
        String memberNo = "123456";
        MemberPo memberPo = new MemberPo();
        memberPo.setMemberNo(memberNo);
        
        when(memberDao.findByMemberNo(memberNo)).thenReturn(memberPo);
        
        // When
        MemberDto result = memberService.findByNo(memberNo);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMemberNo()).isEqualTo(memberNo);
    }
}
```


### 配置管理规范
- 使用`application-{profile}.yml`格式
- 敏感配置通过Nacos配置中心管理
- 数据库连接配置统一管理
- 缓存配置使用`config-map`方式

### 服务间调用规范
- 使用RPC方式进行服务间调用
- 统一使用契约接口定义
- 使用Feign客户端进行服务调用
- 在contract模块中定义服务接口

### 消息发送规范
- 使用`PublisherUtil.send(Topic.XXX, message)`发送消息
```java
import lombok.extern.slf4j.Slf4j;
import wanda.stark.core.codec.JsonUtils;
import wanda.stark.core.msg.Publisher;
@Slf4j
public class PublisherUtil {
    private PublisherUtil() {
    }

    public static void send(String topic, Object body) {
        log.info(">>>发送消息, topic:{}, body:{}", topic, JsonUtils.encode(body));
        try {
            Publisher.get().publish(topic, body);
        } catch (Exception e) {
            log.error(">>>发送消息异常", e);
        }
    }
}
```
- 消息体使用Map或专门的消息对象
- 发送消息后记录日志

## 技术栈特定规范

### Spring Cloud
- 使用`@EnableDiscoveryClient`启用服务发现
- 使用`@ComponentScan(basePackages = {"company.project"})`扫描组件
- 主类命名：`ServiceApplication`、`ApiApplication`、`ConsumerApplication`

### Maven配置
- 使用`wanda.cloud.parent`作为父POM
- 版本号使用`${revision}`占位符
- 模块化组织：contract、provider等

### 字段命名规范
- 使用驼峰命名：`memberNo`、`cinemaCode`
- 布尔字段以`is`开头：`isConsumeHandle`
- 时间字段以`Time`结尾：`settleTime`、`enableTime`

## 代码生成指导

当需要创建新的功能模块时，请遵循以下模式：
1. 在contract模块定义服务接口和DTO
2. 在service模块实现业务逻辑
3. 在api模块提供HTTP接口
4. 如需消息处理，在consumer模块添加消费者
5. 添加相应的测试用例

所有新增的类都应该包含@author注解，作者为fu.wei。

package wanda.card.common.provider.dao;

import org.springframework.stereotype.Repository;
import wanda.card.common.provider.entity.RechargeOrder;
import wanda.stark.db.jsd.JsdReadWriteDao;

import static wanda.card.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.Shortcut.f;

/**
 * 充值订单DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderDao extends JsdReadWriteDao<RechargeOrder, Long> {

    public RechargeOrderDao() {
        super(DB_NAME);
    }

    /**
     * 根据充值订单号查询订单
     *
     * @param rechargeOrderNo 充值订单号
     * @return 充值订单
     */
    public RechargeOrder findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrder.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result()
                .one(RechargeOrder.class);
    }

    /**
     * 检查充值订单号是否存在
     *
     * @param rechargeOrderNo 充值订单号
     * @return 是否存在
     */
    public boolean existsByRechargeOrderNo(String rechargeOrderNo) {
        return findByRechargeOrderNo(rechargeOrderNo) != null;
    }
}

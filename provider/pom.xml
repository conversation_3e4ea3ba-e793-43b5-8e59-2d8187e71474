<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>wanda.card.kam</groupId>
        <artifactId>card-kam-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>card-kam-common-provider</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- 服务starter-->
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>jsd-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>wanda-producer-spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>redis-spring-boot-starter</artifactId>
        </dependency>
        <!-- 服务协议包 -->
        <dependency>
            <groupId>wanda.card.kam</groupId>
            <artifactId>card-kam-common-contract</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>